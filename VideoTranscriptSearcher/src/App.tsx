// React imports
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>oon, <PERSON>Sun, FiGithub, FiPlus } from 'react-icons/fi';
import { Routes, Route, Link } from 'react-router-dom';
import VideoList from './components/VideoList';

// Pages
import CreateVideo from './pages/CreateVideo'; // Using path alias
import VideoDetails from './pages/VideoDetails';

// QueryClient is now managed in main.tsx

function App() {
  const [darkMode, setDarkMode] = useState(() => {
    // Check for dark mode preference at the OS level
    if (typeof window !== 'undefined') {
      const isDark = (
        localStorage.theme === 'dark' ||
        (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)
      );
      console.log('Initial dark mode state:', isDark);
      console.log('localStorage.theme:', localStorage.theme);
      console.log('System prefers dark:', window.matchMedia('(prefers-color-scheme: dark)').matches);
      return isDark;
    }
    return false;
  });

  // Update the theme when darkMode changes
  useEffect(() => {
    console.log('Dark mode changed:', darkMode);
    console.log('Document element classes before:', document.documentElement.className);

    if (darkMode) {
      document.documentElement.classList.add('dark');
      localStorage.theme = 'dark';
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.theme = 'light';
    }

    console.log('Document element classes after:', document.documentElement.className);
  }, [darkMode]);

  return (
      <div className="min-h-screen flex flex-col bg-red-500 dark:bg-gray-900 transition-colors duration-200" style={{backgroundColor: darkMode ? '#111827' : '#ef4444'}}>
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16 items-center">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  Video Transcript Searcher
                </h1>
              </div>
              
              <div className="flex items-center space-x-4">
                <Link
                  to="/create"
                  className="p-2 rounded-full text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  aria-label="Create new video"
                >
                  <FiPlus className="h-5 w-5" />
                </Link>
                
                <button
                  onClick={() => setDarkMode(!darkMode)}
                  className="p-2 rounded-full text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  aria-label={darkMode ? 'Switch to light mode' : 'Switch to dark mode'}
                >
                  {darkMode ? (
                    <FiSun className="h-5 w-5" />
                  ) : (
                    <FiMoon className="h-5 w-5" />
                  )}
                </button>
                
                <a
                  href="https://github.com/yourusername/video-transcript-searcher"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  aria-label="GitHub repository"
                >
                  <FiGithub className="h-5 w-5" />
                </a>
              </div>
            </div>
          </div>
        </header>

        {/* Main content */}
        <main className="flex-grow">
          <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <Routes>
              <Route path="/" element={<VideoList />} />
              <Route path="/create" element={<CreateVideo />} />
              <Route path="/video/:id" element={<VideoDetails />} />
            </Routes>
          </div>
        </main>

        {/* Footer */}
        <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                &copy; {new Date().getFullYear()} Video Transcript Searcher. All rights reserved.
              </p>
              <div className="mt-4 md:mt-0">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Built with React, TypeScript, and Tailwind CSS
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
  );
}

export default App;
